# Electron + Vite + React + TypeScript

A modern Electron application built with Vite, React, and TypeScript.

## Features

- ⚡️ Vite for fast development and building
- ⚛️ React 18 with TypeScript
- 🔧 Electron for desktop app functionality
- 📦 pnpm for package management
- 🏗️ Electron Builder for packaging

## Getting Started

### Prerequisites

- Node.js (v16 or higher)
- pnpm

### Installation

1. Install dependencies:
```bash
pnpm install
```

### Development

Start the development server:
```bash
pnpm dev
```

This will:
1. Start the Vite dev server on http://localhost:5173
2. Wait for the server to be ready
3. Launch the Electron app

### Building

Build for production:
```bash
pnpm build
```

Package the app:
```bash
pnpm pack
```

Create distributable packages:
```bash
pnpm dist
```

## Project Structure

```
app/
├── electron/           # Electron main process files
│   ├── main.ts        # Main process entry point
│   ├── preload.ts     # Preload script
│   └── tsconfig.json  # TypeScript config for Electron
├── src/               # React application source
│   ├── types/         # TypeScript type definitions
│   ├── App.tsx        # Main React component
│   ├── App.css        # App styles
│   ├── main.tsx       # React entry point
│   └── index.css      # Global styles
├── index.html         # HTML template
├── package.json       # Dependencies and scripts
├── tsconfig.json      # TypeScript config for React
├── tsconfig.node.json # TypeScript config for Vite
└── vite.config.ts     # Vite configuration
```

## Scripts

- `pnpm dev` - Start development server
- `pnpm build` - Build for production
- `pnpm preview` - Preview production build
- `pnpm pack` - Package the app
- `pnpm dist` - Create distributable packages
